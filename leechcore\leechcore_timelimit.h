// leechcore_timelimit.h : 时间限制配置头文件
//
// 此文件用于配置 LeechCore 的时间限制功能
// 可以通过修改此文件中的配置来设置不同的过期时间和行为
//
// (c) 2025 - LeechCore 时间限制版本
//

#ifndef __LEECHCORE_TIMELIMIT_H__
#define __LEECHCORE_TIMELIMIT_H__

//-----------------------------------------------------------------------------
// 时间限制配置
//-----------------------------------------------------------------------------

// 过期时间戳 (Unix 时间戳)
// 可以使用在线工具转换时间: https://www.unixtimestamp.com/
// 
// 示例时间戳:
// 1735689600 = 2025-01-01 00:00:00 UTC (2025年1月1日)
// 1738368000 = 2025-02-01 00:00:00 UTC (2025年2月1日)
// 1740787200 = 2025-03-01 00:00:00 UTC (2025年3月1日)
// 1743465600 = 2025-04-01 00:00:00 UTC (2025年4月1日)
// 1746057600 = 2025-05-01 00:00:00 UTC (2025年5月1日)
// 1748736000 = 2025-06-01 00:00:00 UTC (2025年6月1日)
//
// 设置为 0 表示禁用时间限制
#define LEECHCORE_EXPIRY_TIMESTAMP  1735689600

// 时间限制行为配置
#define LEECHCORE_TIMELIMIT_ENABLED         1    // 1=启用时间限制, 0=禁用时间限制
#define LEECHCORE_SHOW_EXPIRY_WARNING       1    // 1=显示过期警告, 0=静默失败
#define LEECHCORE_ALLOW_READ_AFTER_EXPIRY   0    // 1=过期后仍允许读取, 0=过期后禁止读取
#define LEECHCORE_ALLOW_WRITE_AFTER_EXPIRY  0    // 1=过期后仍允许写入, 0=过期后禁止写入

// 警告消息配置
#define LEECHCORE_EXPIRY_WARNING_TITLE      "LeechCore 时间限制版本"
#define LEECHCORE_EXPIRY_WARNING_MESSAGE    "此版本已过期，请联系开发者获取新版本或完整授权"

//-----------------------------------------------------------------------------
// 时间限制功能函数声明
//-----------------------------------------------------------------------------

#ifdef __cplusplus
extern "C" {
#endif

/*
* 检查时间限制是否已过期
* -- return = TRUE 如果时间已过期，FALSE 如果仍在有效期内
*/
BOOL LcCheckTimeExpiry();

/*
* 显示时间过期警告信息
*/
VOID LcShowExpiryWarning();

/*
* 获取剩余有效时间（秒）
* -- return = 剩余秒数，如果已过期则返回 0
*/
QWORD LcGetRemainingTime();

/*
* 检查是否允许读取操作
* -- return = TRUE 如果允许读取，FALSE 如果禁止读取
*/
BOOL LcIsReadAllowed();

/*
* 检查是否允许写入操作
* -- return = TRUE 如果允许写入，FALSE 如果禁止写入
*/
BOOL LcIsWriteAllowed();

#ifdef __cplusplus
}
#endif

//-----------------------------------------------------------------------------
// 编译时配置检查
//-----------------------------------------------------------------------------

#if LEECHCORE_TIMELIMIT_ENABLED && (LEECHCORE_EXPIRY_TIMESTAMP == 0)
#error "时间限制已启用但未设置过期时间戳，请设置 LEECHCORE_EXPIRY_TIMESTAMP"
#endif

#if LEECHCORE_EXPIRY_TIMESTAMP > 0 && LEECHCORE_EXPIRY_TIMESTAMP < 1640995200
#error "过期时间戳似乎无效（小于2022年），请检查时间戳设置"
#endif

#endif /* __LEECHCORE_TIMELIMIT_H__ */
