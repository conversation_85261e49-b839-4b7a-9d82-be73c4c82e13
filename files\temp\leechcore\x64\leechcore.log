﻿  64 bit Processing .\leechrpc.idl
  leechrpc.idl
  device_file.c
  device_fpga.c
  device_hibr.c
  device_pmem.c
  device_tmd.c
  device_usb3380.c
  device_vmm.c
  device_vmware.c
  leechcore.c
  leechrpcshared.c
  leechrpcclient.c
  leechrpc_c.c
  memmap.c
  ob_bytequeue.c
  ob_core.c
  ob_map.c
  ob_set.c
  oscompatibility.c
  util.c
    正在创建库 C:\Users\<USER>\Desktop\test\LeechCore备份\files\\lib\leechcore.lib 和对象 C:\Users\<USER>\Desktop\test\LeechCore备份\files\\lib\leechcore.exp
  正在生成代码
  已完成代码的生成
  leechcore.vcxproj -> C:\Users\<USER>\Desktop\test\LeechCore备份\files\leechcore.dll
  文件名、目录名或卷标语法不正确。
C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\Microsoft.CppCommon.targets(166,5): error MSB3073: 命令“if not exist "C:\Users\<USER>\Desktop\test\LeechCore备份\\includes\lib64" mkdir "C:\Users\<USER>\Desktop\test\LeechCore备份\\includes\lib64"
C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\Microsoft.CppCommon.targets(166,5): error MSB3073: copy "C:\Users\<USER>\Desktop\test\LeechCore备份\files\\lib\leechcore.lib" "C:\Users\<USER>\Desktop\test\LeechCore备份\\includes\lib64\" /y
C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\Microsoft.CppCommon.targets(166,5): error MSB3073: copy "C:\Users\<USER>\Desktop\test\LeechCore备份\leechcore\\leechcore.h" "C:\Users\<USER>\Desktop\test\LeechCore备份\\includes\" /y
C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\Microsoft.CppCommon.targets(166,5): error MSB3073: 
C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\Microsoft.CppCommon.targets(166,5): error MSB3073: :VCEnd”已退出，代码为 123。
